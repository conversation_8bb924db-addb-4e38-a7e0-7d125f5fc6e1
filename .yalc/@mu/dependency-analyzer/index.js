const process = require('node:process');
const { inspect } = require('node:util');
const pc = require('picocolors');
const { promises: fs } = require('fs');
const path = require('path');
const { version } = require('node:os');

/**
 * 集成依赖分析工具
 * 功能：
 * 1. 分析项目中实际使用的 npm 包依赖
 * 2. 递归分析这些包的所有嵌套依赖关系
 * 3. 检测依赖包中是否存在版本冲突
 */

class DependencyAnalyzer {
  constructor(options = {}) {
    this.options = {
      exclude: options.exclude || [],
      outputFormat: options.outputFormat || 'console', // console, json, yaml
      outputFile: options.outputFile || null,
      verbose: options.verbose || false,
      ...options
    };

    // webpack 别名配置（从 config/index.js 和 config/extend.js 动态获取）
    this.webpackAliases = this.loadWebpackAliases();

    this.results = {
      actualUsedPackages: [],
      nestedDependencies: {},
      versionConflicts: {},
      summary: {
        totalPackages: 0,
        conflictCount: 0,
        analysisTime: null
      }
    };
  }

  /**
   * 从配置文件中加载 webpack 别名
   */
  loadWebpackAliases() {
    const aliases = new Set();

    try {
      // 获取项目根目录
      const projectRoot = process.cwd();

      // 解析 config/index.js 文件内容
      const indexConfigPath = path.join(projectRoot, 'config/index.js');
      if (require('fs').existsSync(indexConfigPath)) {
        const indexAliases = this.parseConfigFileForAliases(indexConfigPath);
        indexAliases.forEach(alias => aliases.add(alias));
        if (this.options.verbose && indexAliases.length > 0) {
          console.log(pc.gray(`📋 从 config/index.js 解析别名: ${indexAliases.join(', ')}`));
        }
      }

      // 解析 config/extend.js 文件内容
      const extendConfigPath = path.join(projectRoot, 'config/extend.js');
      if (require('fs').existsSync(extendConfigPath)) {
        const extendAliases = this.parseConfigFileForAliases(extendConfigPath);
        extendAliases.forEach(alias => aliases.add(alias));
        if (this.options.verbose && extendAliases.length > 0) {
          console.log(pc.gray(`📋 从 config/extend.js 解析别名: ${extendAliases.join(', ')}`));
        }
      }

      if (this.options.verbose) {
        console.log(pc.gray(`📋 合并后的 webpack 别名: ${Array.from(aliases).join(', ')}`));
      }

    } catch (error) {
      console.warn(pc.yellow(`⚠️  加载 webpack 别名配置时出错: ${error.message}`));
    }

    // 如果没有加载到任何别名，使用默认配置
    if (aliases.size === 0) {
      const defaultAliases = [
        '@src', '@api', '@comp', '@components', '@utils', '@styles',
        '@config', '@mucfc.com', '@models', '@services', '@assets', '@types',
        '@store', '@dev', '@repayment/store'
      ];
      defaultAliases.forEach(alias => aliases.add(alias));

      if (this.options.verbose) {
        console.log(pc.gray(`📋 使用默认 webpack 别名: ${defaultAliases.join(', ')}`));
      }
    }

    return aliases;
  }

  /**
   * 解析配置文件内容，提取 alias 配置
   */
  parseConfigFileForAliases(configPath) {
    const aliases = [];

    try {
      const content = require('fs').readFileSync(configPath, 'utf8');

      // 使用正则表达式匹配 alias 配置
      // 匹配 alias: { ... } 或 alias: { ... },
      const aliasRegex = /alias\s*:\s*\{([^}]+)\}/g;
      let match;

      while ((match = aliasRegex.exec(content)) !== null) {
        const aliasBlock = match[1];

        // 提取每个别名键
        // 匹配 '@xxx': 或 "@xxx": 格式
        const keyRegex = /['"](@[^'"]+)['"]\s*:/g;
        let keyMatch;

        while ((keyMatch = keyRegex.exec(aliasBlock)) !== null) {
          aliases.push(keyMatch[1]);
        }
      }

    } catch (error) {
      if (this.options.verbose) {
        console.log(pc.yellow(`⚠️  解析配置文件 ${configPath} 时出错: ${error.message}`));
      }
    }

    return aliases;
  }

  /**
   * 主要分析入口
   */
  async analyze() {
    const startTime = Date.now();
    
    try {
      console.log(pc.blue('🔍 开始依赖分析...'));
      
      // 阶段1：分析实际使用的包
      console.log(pc.cyan('\n📦 阶段1: 分析实际使用的包...'));
      await this.analyzeActualUsedPackages();
      
      // 阶段2：递归分析嵌套依赖
      console.log(pc.cyan('\n🔗 阶段2: 分析嵌套依赖关系...'));
      await this.analyzeNestedDependencies();
      
      // 阶段3：检测版本冲突
      console.log(pc.cyan('\n⚠️  阶段3: 检测版本冲突...'));
      await this.detectVersionConflicts();
      
      // 生成汇总信息
      this.results.summary.analysisTime = Date.now() - startTime;
      this.results.summary.totalPackages = this.results.actualUsedPackages.length;
      this.results.summary.conflictCount = Object.keys(this.results.versionConflicts).length;
      
      // 输出结果
      await this.outputResults();
      
      console.log(pc.green(`\n✅ 分析完成！耗时: ${this.results.summary.analysisTime}ms`));
      
      // 如果有冲突，返回错误码
      if (this.results.summary.conflictCount > 0) {
        process.exit(1);
      }
      
    } catch (error) {
      console.error(pc.red('❌ 分析过程中发生错误:'));
      console.error(error.stack);
      process.exit(1);
    }
  }

  /**
   * 阶段1：分析实际使用的包
   * 使用 depcheck 工具分析项目代码中实际引用的 npm 包
   */
  async analyzeActualUsedPackages() {
    try {
      // 导入 depcheck
      const depcheck = require('depcheck');
      
      const options = {
        ignoreBinPackage: false,
        skipMissing: false,
        package: require(path.join(process.cwd(), 'package.json'))
      };

      // 只扫描 src 目录
      const srcPath = require('path').join(process.cwd(), 'src');
      const result = await depcheck(srcPath, options);

      // depcheck.using 包含实际使用的包
      const usedPackages = Object.keys(result.using).filter(pkg => {
        // 排除 webpack 别名 - 检查包名是否包含任何别名字符串
        for (const alias of this.webpackAliases) {
          if (pkg.includes(alias)) return false;
        }

        // 排除用户指定的排除包
        if (this.options.exclude.includes(pkg)) return false;

        return true;
      });
      
      // 读取 package.json 和 package-lock.json 获取版本信息
      const packageJson = require(path.join(process.cwd(), 'package.json'));
      let packageLock = null;
      try {
        packageLock = JSON.parse(await fs.readFile('package-lock.json', 'utf8'));
      } catch (error) {
        console.warn('   无法读取 package-lock.json，将使用 package.json 中的版本信息');
      }

      this.results.actualUsedPackages = usedPackages.map(pkg => {
        let version = null;

        // 优先从 package-lock.json 获取精确版本
        if (packageLock && packageLock.dependencies && packageLock.dependencies[pkg]) {
          version = packageLock.dependencies[pkg].version;
        }
        // 如果 package-lock.json 中没有，从 package.json 获取版本范围
        else if (packageJson.dependencies && packageJson.dependencies[pkg]) {
          version = packageJson.dependencies[pkg];
        }
        else if (packageJson.devDependencies && packageJson.devDependencies[pkg]) {
          version = packageJson.devDependencies[pkg];
        }

        return {
          name: pkg,
          type: 'dependency',
          version: version
        };
      });
      
      if (this.options.verbose) {
        console.log(`   找到 ${this.results.actualUsedPackages.length} 个实际使用的包`);
        this.results.actualUsedPackages.forEach(pkg => {
          console.log(`   - ${pkg.name} (${pkg.type})`);
        });
      }
      
    } catch (error) {
      throw new Error(`分析实际使用包时出错: ${error.message}`);
    }
  }

  /**
   * 阶段2：递归分析嵌套依赖
   */
  async analyzeNestedDependencies() {
    try {
      // 读取 package-lock.json 获取完整的依赖树
      const lockFile = JSON.parse(await fs.readFile('package-lock.json', 'utf8'));
      
      // 为每个实际使用的包分析其嵌套依赖
      for (const pkg of this.results.actualUsedPackages) {
        this.results.nestedDependencies[pkg.name] = this.extractNestedDependencies(
          lockFile.dependencies, 
          pkg.name,
          pkg.version,
        );
      }
      
      if (this.options.verbose) {
        console.log(`   分析了 ${Object.keys(this.results.nestedDependencies).length} 个包的嵌套依赖`);
        console.log(inspect(this.results.nestedDependencies, {
          colors: true,
          depth: 3,  // null - 显示所有嵌套层级（默认只显示3层）
        }));
      }
      
    } catch (error) {
      throw new Error(`分析嵌套依赖时出错: ${error.message}`);
    }
  }

  /**
   * 递归提取嵌套依赖
   */
  extractNestedDependencies(dependencies, packageName, rootVersion, visited = new Set()) {
    if (!dependencies || !dependencies[packageName] || visited.has(packageName)) {
      return {};
    }

    visited.add(packageName);
    const pkg = dependencies[packageName];
    const result = {version: rootVersion};

    // 合并 requires 和 dependencies
    const allDeps = {};

    // 首先添加 requires 中声明的依赖
    if (pkg.requires) {
      for (const [depName, version] of Object.entries(pkg.requires)) {
        allDeps[depName] = { version, source: 'requires' };
      }
    }

    // 然后添加实际安装的 dependencies，可能会覆盖 requires 中的版本
    if (pkg.dependencies) {
      for (const [depName, depInfo] of Object.entries(pkg.dependencies)) {
        allDeps[depName] = {
          version: depInfo.version,
          source: 'dependencies',
          nested: this.extractNestedDependencies(pkg.dependencies, depName, new Set(visited))
        };
      }
    }

    // 对于只在 requires 中的依赖，尝试从顶层 dependencies 中找到版本信息
    for (const [depName, depInfo] of Object.entries(allDeps)) {
      if (depInfo.source === 'requires' && dependencies[depName]) {
          result[depName] = {
            version: dependencies[depName].version,
            nested: this.extractNestedDependencies(dependencies, depName, new Set(visited))
          };
      } else if (depInfo.source === 'dependencies') {
        result[depName] = {
          version: depInfo.version,
          nested: depInfo.nested
        };
      }
    }

    return result;
  }

  /**
   * 阶段3：检测版本冲突
   * 基于阶段二分析的嵌套依赖数据
   */
  async detectVersionConflicts() {
    try {
      // 从嵌套依赖中提取所有包的版本信息
      const allPackages = this.flattenDependencies();

      // 检测版本冲突
      const conflicts = this.findVersionConflicts(allPackages);

      this.results.versionConflicts = conflicts;

      if (this.options.verbose) {
        console.log(`   发现 ${Object.keys(conflicts).length} 个版本冲突`);
      }

    } catch (error) {
      throw new Error(`检测版本冲突时出错: ${error.message}`);
    }
  }

  /**
   * 将嵌套依赖打平成扁平的包列表
   */
  flattenDependencies() {
    const allPackages = new Map(); // packageName -> Set of {version, path}

    // 遍历所有实际使用的包的嵌套依赖
    for (const [rootPackage, nestedDeps] of Object.entries(this.results.nestedDependencies)) {
      console.log('rootPackage', rootPackage);
      console.log('nestedDeps', nestedDeps);

      // 找到根包的版本信息
      const rootPackageInfo = this.results.actualUsedPackages.find(pkg => pkg.name === rootPackage);
      const rootVersion = rootPackageInfo ? rootPackageInfo.version : 'unknown';

      // 初始化根包的版本集合
      if (!allPackages.has(rootPackage)) {
        allPackages.set(rootPackage, new Set());
      }

      // 添加根包的版本和路径信息
      allPackages.get(rootPackage).add({
        version: rootVersion,
        path: rootPackage
      });

      this.collectPackageVersions(nestedDeps, allPackages, [rootPackage]);
    }

    return allPackages;
  }

  /**
   * 递归收集包版本信息
   */
  collectPackageVersions(dependencies, allPackages, currentPath = []) {
    for (const [packageName, depInfo] of Object.entries(dependencies)) {
      const packagePath = [...currentPath, packageName].join(' -> ');

      // 初始化包的版本集合
      if (!allPackages.has(packageName)) {
        allPackages.set(packageName, new Set());
      }

      // 添加版本和路径信息
      allPackages.get(packageName).add({
        version: depInfo.version,
        path: packagePath
      });

      // 递归处理嵌套依赖
      if (depInfo.nested && Object.keys(depInfo.nested).length > 0) {
        this.collectPackageVersions(depInfo.nested, allPackages, [...currentPath, packageName]);
      }
    }
  }

  /**
   * 在扁平的包列表中查找版本冲突
   */
  findVersionConflicts(allPackages) {
    const conflicts = {};

    for (const [packageName, versionSet] of allPackages) {
      // 将 Set 转换为数组以便处理
      const versions = Array.from(versionSet);

      // 如果同一个包有多个不同版本，则存在冲突
      const uniqueVersions = new Set(versions.map(v => v.version));
      if (uniqueVersions.size > 1) {
        // 按版本分组
        const versionGroups = {};
        versions.forEach(({ version, path }) => {
          if (!versionGroups[version]) {
            versionGroups[version] = [];
          }
          versionGroups[version].push(path);
        });

        conflicts[packageName] = versionGroups;
      }
    }

    return conflicts;
  }

  /**
   * 输出结果
   */
  async outputResults() {
    switch (this.options.outputFormat) {
      case 'json':
        await this.outputJSON();
        break;
      case 'yaml':
        await this.outputYAML();
        break;
      default:
        this.outputConsole();
    }
  }

  /**
   * 控制台输出
   */
  outputConsole() {
    console.log(pc.blue('\n📊 依赖分析报告'));
    console.log('='.repeat(50));

    // 汇总信息
    console.log(pc.yellow('\n📈 汇总信息:'));
    console.log(`   实际使用的包数量: ${this.results.summary.totalPackages}`);
    console.log(`   版本冲突数量: ${this.results.summary.conflictCount}`);
    console.log(`   分析耗时: ${this.results.summary.analysisTime}ms`);

    // 实际使用的包
    if (this.results.actualUsedPackages.length > 0) {
      console.log(pc.yellow('\n📦 实际使用的包:'));
      this.results.actualUsedPackages.forEach(pkg => {
        console.log(`   ${pc.green(pkg.name)} (${pkg.type})`);
      });
    }

    // 版本冲突
    if (Object.keys(this.results.versionConflicts).length > 0) {
      console.log(pc.red('\n⚠️  版本冲突:'));
      for (const [name, conflicts] of Object.entries(this.results.versionConflicts)) {
        console.log(`\n${pc.red(name)}:`);
        console.log(inspect(conflicts, { colors: true }));
      }
    } else {
      console.log(pc.green('\n✅ 未发现版本冲突'));
    }
  }

  /**
   * JSON 格式输出
   */
  async outputJSON() {
    const output = JSON.stringify(this.results, null, 2);

    if (this.options.outputFile) {
      await fs.writeFile(this.options.outputFile, output);
      console.log(pc.green(`结果已保存到: ${this.options.outputFile}`));
    } else {
      console.log(output);
    }
  }

  /**
   * YAML 格式输出
   */
  async outputYAML() {
    // 简单的 YAML 输出实现
    const yamlOutput = this.convertToYAML(this.results);

    if (this.options.outputFile) {
      await fs.writeFile(this.options.outputFile, yamlOutput);
      console.log(pc.green(`结果已保存到: ${this.options.outputFile}`));
    } else {
      console.log(yamlOutput);
    }
  }

  /**
   * 简单的 YAML 转换
   */
  convertToYAML(obj, indent = 0) {
    const spaces = '  '.repeat(indent);
    let yaml = '';

    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        yaml += `${spaces}${key}:\n${this.convertToYAML(value, indent + 1)}`;
      } else if (Array.isArray(value)) {
        yaml += `${spaces}${key}:\n`;
        value.forEach(item => {
          if (typeof item === 'object') {
            yaml += `${spaces}- \n${this.convertToYAML(item, indent + 2)}`;
          } else {
            yaml += `${spaces}- ${item}\n`;
          }
        });
      } else {
        yaml += `${spaces}${key}: ${value}\n`;
      }
    }

    return yaml;
  }
}

module.exports = DependencyAnalyzer;
